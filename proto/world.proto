syntax = "proto3";

package world;

service WorldService {
  rpc <PERSON>haracter(CharacterRequest) returns (CharacterResponse);
  rpc ChangeMap(ChangeMapRequest) returns (ChangeMapResponse);
  rpc MoveCharacter(CharacterMoveRequest) returns (CharacterMoveResponse);
  rpc GetTargetHp(ObjectHpRequest) returns (ObjectHpResponse);
}

message CharacterRequest {
  string token = 1;
  string user_id = 2;
  string char_id = 3;
  string session_id = 4;
}

message CharacterResponse {
  int32 count = 1;
}

message CharacterMoveRequest {
  string session_id = 1;
  uint32 target_id = 2;
  float x = 3;
  float y = 4;
  float z = 5;
}

message CharacterMoveResponse {
  int32 id = 1;
  int32 target_id = 2;
  int32 distance = 3;
  float x = 4;
  float y = 5;
  float z = 6;
}

message ChangeMapRequest {
  int32 id = 1;
  float x = 2;
  float y = 3;
}

message ChangeMapResponse {
  int32 id = 1;
  int32 map_id = 2;
  float x = 3;
  float y = 4;
  int32 move_mode = 5;
  int32 ride_mode = 6;
}

message AttackRequest {
  string session_id = 1;
  uint32 target_id = 2;
}

message ObjectHpRequest {
  string session_id = 1;
  uint32 target_id = 2;
}

message ObjectHpResponse {
  uint32 target_id = 1;
  int32 hp = 2;
}
