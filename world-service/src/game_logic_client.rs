use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{mpsc, Mutex};
use tonic::transport::Channel;
use tracing::{debug, error, info, warn};
use futures::{Stream, StreamExt};
use std::pin::Pin;

pub mod game_logic {
    tonic::include_proto!("game_logic");
}

pub mod game {
    tonic::include_proto!("game");
}

use game_logic::game_logic_service_client::GameLogicServiceClient;
use game::event_service_client::EventServiceClient;

pub struct GameLogicClientManager {
    clients: Arc<Mutex<HashMap<u32, GameLogicClient>>>,
}

pub struct GameLogicClient {
    pub map_id: u32,
    pub endpoint: String,
    pub service_client: Option<GameLogicServiceClient<Channel>>,
    pub event_client: Option<EventServiceClient<Channel>>,
    pub event_sender: Option<mpsc::UnboundedSender<game::GenericEvent>>,
}

impl GameLogicClientManager {
    pub fn new() -> Self {
        Self {
            clients: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    pub async fn add_client(&self, map_id: u32, endpoint: String) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut clients = self.clients.lock().await;
        
        if clients.contains_key(&map_id) {
            warn!("Game logic client for map {} already exists", map_id);
            return Ok(());
        }

        let mut client = GameLogicClient {
            map_id,
            endpoint: endpoint.clone(),
            service_client: None,
            event_client: None,
            event_sender: None,
        };

        // Connect to the game logic service
        match GameLogicServiceClient::connect(endpoint.clone()).await {
            Ok(service_client) => {
                client.service_client = Some(service_client);
                info!("Connected to game logic service for map {} at {}", map_id, endpoint);
            }
            Err(e) => {
                error!("Failed to connect to game logic service for map {} at {}: {}", map_id, endpoint, e);
                return Err(e.into());
            }
        }

        // Connect to the event service
        match EventServiceClient::connect(endpoint.clone()).await {
            Ok(event_client) => {
                client.event_client = Some(event_client);
                info!("Connected to game logic event service for map {} at {}", map_id, endpoint);
            }
            Err(e) => {
                error!("Failed to connect to game logic event service for map {} at {}: {}", map_id, endpoint, e);
                return Err(e.into());
            }
        }

        clients.insert(map_id, client);
        Ok(())
    }

    pub async fn remove_client(&self, map_id: u32) {
        let mut clients = self.clients.lock().await;
        if let Some(_client) = clients.remove(&map_id) {
            info!("Removed game logic client for map {}", map_id);
        }
    }

    pub async fn get_nearby_objects(
        &self,
        map_id: u32,
        session_id: String,
        x: f32,
        y: f32,
        z: f32,
    ) -> Result<game_logic::NearbyObjectsResponse, Box<dyn std::error::Error + Send + Sync>> {
        let mut clients = self.clients.lock().await;
        
        if let Some(client) = clients.get_mut(&map_id) {
            if let Some(service_client) = &mut client.service_client {
                let request = game_logic::NearbyObjectsRequest {
                    session_id,
                    x,
                    y,
                    z,
                    map_id: map_id as i32,
                };

                let response = service_client.get_nearby_objects(request).await?;
                return Ok(response.into_inner());
            }
        }

        Err(format!("No game logic client found for map {}", map_id).into())
    }

    pub async fn start_event_stream(
        &self,
        map_id: u32,
        outbound_receiver: mpsc::UnboundedReceiver<game::GenericEvent>,
    ) -> Result<mpsc::UnboundedReceiver<game::GenericEvent>, Box<dyn std::error::Error + Send + Sync>> {
        let mut clients = self.clients.lock().await;
        
        if let Some(client) = clients.get_mut(&map_id) {
            if let Some(mut event_client) = client.event_client.take() {
                let (inbound_sender, inbound_receiver) = mpsc::unbounded_channel();
                
                // Create the bidirectional stream
                let outbound_stream = tokio_stream::wrappers::UnboundedReceiverStream::new(outbound_receiver);
                
                let response = event_client.stream_events(outbound_stream).await?;
                let mut inbound_stream = response.into_inner();

                // Spawn task to handle incoming events
                tokio::spawn(async move {
                    while let Some(event) = inbound_stream.next().await {
                        match event {
                            Ok(game_event) => {
                                debug!("Received event from game logic for map {}: {:?}", map_id, game_event);
                                if let Err(e) = inbound_sender.send(game_event) {
                                    error!("Failed to forward event from game logic for map {}: {}", map_id, e);
                                    break;
                                }
                            }
                            Err(e) => {
                                error!("Error receiving event from game logic for map {}: {}", map_id, e);
                                break;
                            }
                        }
                    }
                    info!("Event stream from game logic for map {} ended", map_id);
                });

                return Ok(inbound_receiver);
            }
        }

        Err(format!("No event client found for map {}", map_id).into())
    }

    pub async fn send_event(&self, map_id: u32, event: game::GenericEvent) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let clients = self.clients.lock().await;
        
        if let Some(client) = clients.get(&map_id) {
            if let Some(sender) = &client.event_sender {
                sender.send(event)?;
                return Ok(());
            }
        }

        Err(format!("No event sender found for map {}", map_id).into())
    }

    pub async fn list_connected_maps(&self) -> Vec<u32> {
        let clients = self.clients.lock().await;
        clients.keys().cloned().collect()
    }
}

impl GameLogicClient {
    pub async fn connect(map_id: u32, endpoint: String) -> Result<Self, Box<dyn std::error::Error + Send + Sync>> {
        let service_client = GameLogicServiceClient::connect(endpoint.clone()).await?;
        let event_client = EventServiceClient::connect(endpoint.clone()).await?;

        Ok(Self {
            map_id,
            endpoint,
            service_client: Some(service_client),
            event_client: Some(event_client),
            event_sender: None,
        })
    }
}
