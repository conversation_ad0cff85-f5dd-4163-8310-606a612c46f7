[package]
name = "world-service"
version = "0.1.0"
edition = "2021"

[dependencies]
utils = { path = "../utils" }
dotenv = "0.15"
tokio = { version = "1.41.1", features = ["full"] }
tokio-stream = "0.1"
serde_json = "1.0.140"
kube = { version = "1.1.0", features = ["runtime", "derive"] }
k8s-openapi = { version = "0.25.0", features = ["latest"] }
tracing = "0.1.41"
tonic = "0.12"
futures = "0.3"

[build-dependencies]
tonic-build = "0.12.3"
