fn main() {
    // gRPC Client code
    tonic_build::configure()
        .build_server(false) // Generate gRPC server code?
        .compile_well_known_types(true)
        .type_attribute(".", "#[derive(serde::Serialize, serde::Deserialize)]")
        .compile_protos(
            &[
                "../proto/auth.proto",
                "../proto/chat.proto",
                "../proto/character.proto",
                "../proto/character_common.proto",
                "../proto/game.proto"
            ],
            &["../proto"],
        )
        .unwrap_or_else(|e| panic!("Failed to compile protos {:?}", e));
}
