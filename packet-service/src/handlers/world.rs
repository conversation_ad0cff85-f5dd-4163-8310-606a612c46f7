use crate::character_client::CharacterClient;
use crate::connection_service::ConnectionService;
use crate::packet::{send_packet, Packet, PacketPayload};
use crate::packet_type::PacketType;
use crate::world_client::WorldClientManager;
use chrono::{Local, Timelike};
use std::error::Error;
use std::sync::Arc;
use tokio::net::TcpStream;
use tokio::sync::Mutex;
use tonic::transport::Channel;
use tracing::{debug, error, info, warn};
use utils::service_discovery::get_kube_service_endpoints_by_dns;

fn distance(x1: f64, y1: f64, x2: f64, y2: f64) -> u16 {
    let dist = ((x2 - x1).powi(2) + (y2 - y1).powi(2)).sqrt();
    dist.round() as u16
}

pub(crate) async fn handle_change_map_req(
    packet: Packet,
    character_client: Arc<Mutex<CharacterClient>>,
    connection_service: Arc<ConnectionService>,
    connection_id: String,
) -> Result<(), Box<dyn Error + Send + Sync>> {
    use crate::packets::cli_change_map_req::*;
    use crate::packets::srv_change_map_reply::*;
    let request = CliChangeMapReq::decode(packet.payload.as_slice())?;
    debug!("{:?}", request);

    let mut user_id = "".to_string();
    let mut char_id = 0;
    let mut client_id = 0;
    let mut character_id_list: Vec<u32> = Vec::new();
    let session_id;
    if let Some(mut state) = connection_service.get_connection(&connection_id) {
        user_id = state.user_id.expect("Missing user id in connection state");
        session_id = state.session_id.expect("Missing session id in connection state");
        char_id = state.character_id.expect("Missing character id in connection state");
        character_id_list = state.character_list.clone().expect("Missing character id list");
        client_id = state.client_id;
    }

    let mut character_client = character_client.lock().await;
    let character_data = character_client
        .get_character(&user_id.to_string(), character_id_list[char_id as usize] as u8)
        .await?;

    let character = character_data.character.unwrap_or_default();
    let stats = character.stats.unwrap();

    let now = Local::now();
    let time_as_u16 = (now.hour() * 100 + now.minute()) as u16;

    let data = SrvChangeMapReply {
        object_index: client_id,
        hp: stats.hp as u16,
        mp: stats.mp as u16,
        xp: stats.xp as u16,
        penalize_xp: stats.penalty_xp as u16,
        craft_rate: 0,
        update_time: 0,
        world_rate: 1,
        town_rate: 1,
        item_rate: [0u8; 11],
        flags: 1,
        world_time: time_as_u16,
        team_number: 10,
    };
    let response_packet = Packet::new(PacketType::PakwcChangeMapReply, &data)?;
    if let Some(mut state) = connection_service.get_connection_mut(&connection_id) {
        let writer_clone = state.writer.clone().unwrap();
        let mut locked_stream = writer_clone.lock().await;
        send_packet(&mut locked_stream, &response_packet).await?;
    }

    Ok(())
}

pub(crate) async fn handle_mouse_cmd_req(
    packet: Packet,
    connection_service: Arc<ConnectionService>,
    connection_id: String,
) -> Result<(), Box<dyn Error + Send + Sync>> {
    use crate::packets::cli_mouse_cmd::*;
    use crate::packets::srv_mouse_cmd::*;
    let request = CliMouseCmd::decode(packet.payload.as_slice())?;
    debug!("{:?}", request);

    let mut char_id = 0;
    let mut client_id = 0;
    let mut character_id_list: Vec<u32> = Vec::new();
    if let Some(mut state) = connection_service.get_connection(&connection_id) {
        char_id = state.character_id.expect("Missing character id in connection state");
        character_id_list = state.character_list.clone().expect("Missing character id list");
        client_id = state.client_id;
    }

    let data = SrvMouseCmd {
        id: client_id,
        target_id: request.target_id,
        distance: distance(520000 as f64, 520000 as f64, request.x as f64, request.y as f64),
        x: request.x,
        y: request.y,
        z: request.z,
    };
    let response_packet = Packet::new(PacketType::PakwcMouseCmd, &data)?;
    if let Some(mut state) = connection_service.get_connection_mut(&connection_id) {
        let writer_clone = state.writer.clone().unwrap();
        let mut locked_stream = writer_clone.lock().await;
        send_packet(&mut locked_stream, &response_packet).await?;
    }
    Ok(())
}

// Function to send movement updates to world service
pub(crate) async fn send_movement_to_world_service(
    world_client_manager: Arc<WorldClientManager>,
    connection_service: Arc<ConnectionService>,
    connection_id: String,
    x: f32,
    y: f32,
    z: f32,
) -> Result<(), Box<dyn Error + Send + Sync>> {
    if let Some(connection_state) = connection_service.get_connection(&connection_id) {
        if let Some(session_id) = &connection_state.session_id {
            // Send movement event to world service
            if let Err(e) = world_client_manager
                .send_client_move_event(session_id, x, y, z)
                .await
            {
                error!("Failed to send move event to world service: {}", e);
                return Err(e);
            }

            debug!("Sent move event for client {} to world service", session_id);
        }
    }
    Ok(())
}

pub(crate) async fn handle_togggle_move_req(
    packet: Packet,
    connection_service: Arc<ConnectionService>,
    connection_id: String,
) -> Result<(), Box<dyn Error + Send + Sync>> {
    use crate::packets::cli_toggle_move::*;
    use crate::packets::srv_toggle_move::*;
    let request = CliToggleMove::decode(packet.payload.as_slice())?;
    debug!("{:?}", request);

    let mut client_id = 0;
    if let Some(mut state) = connection_service.get_connection(&connection_id) {
        client_id = state.client_id;
    }
    let mut toggle_type = 0;;
    {
        use crate::packets::cli_toggle_move::ToggleMove;
        match request.type_ {
            ToggleMove::Run => { toggle_type = 2; }
            ToggleMove::Sit => { toggle_type = 8; }
            ToggleMove::Drive => { toggle_type = 9; }
        }
    }

    use crate::packets::srv_toggle_move::ToggleMove;
    let mut final_type = ToggleMove::Run;
    {
        match toggle_type {
            0 => { final_type = ToggleMove::Run; }
            1 => { final_type = ToggleMove::Sit; }
            2 => { final_type = ToggleMove::Drive; }
            _ => { final_type = ToggleMove::Run; }
        }
    }

    let data = SrvToggleMove {
        object_id: client_id,
        type_: final_type,
        run_speed: 0,
    };
    let response_packet = Packet::new(PacketType::PakwcToggleMove, &data)?;
    if let Some(mut state) = connection_service.get_connection_mut(&connection_id) {
        let writer_clone = state.writer.clone().unwrap();
        let mut locked_stream = writer_clone.lock().await;
        send_packet(&mut locked_stream, &response_packet).await?;
    }
    Ok(())
}

pub(crate) async fn handle_set_animation_req(
    packet: Packet,
    connection_service: Arc<ConnectionService>,
    connection_id: String,
) -> Result<(), Box<dyn Error + Send + Sync>> {
    use crate::packets::cli_set_animation::*;
    use crate::packets::srv_set_animation::*;
    let request = CliSetAnimation::decode(packet.payload.as_slice())?;
    debug!("{:?}", request);

    let mut client_id = 0;
    if let Some(mut state) = connection_service.get_connection(&connection_id) {
        client_id = state.client_id;
    }

    let data = SrvSetAnimation {
        id: request.id,
        value: request.value,
        object_id: client_id,
    };
    let response_packet = Packet::new(PacketType::PacwcSetAnimation, &data)?;
    if let Some(mut state) = connection_service.get_connection_mut(&connection_id) {
        let writer_clone = state.writer.clone().unwrap();
        let mut locked_stream = writer_clone.lock().await;
        send_packet(&mut locked_stream, &response_packet).await?;
    }
    Ok(())
}

// New world service integration handlers

pub async fn handle_character_spawn(
    packet: Packet,
    world_client_manager: Arc<WorldClientManager>,
    connection_service: Arc<ConnectionService>,
    connection_id: String,
    world_url: String,
) -> Result<(), Box<dyn Error + Send + Sync>> {
    debug!("Handling character spawn packet: {:?}", packet);

    if let Some(mut connection_state) = connection_service.get_connection_mut(&connection_id) {
        if let (Some(session_id), Some(character_id)) = (&connection_state.session_id, connection_state.character_id) {
            let client_id = connection_id.clone();
            let map_id = 1; // Default map for now - should be retrieved from character data

            // Connect to world service for this client
            if let Err(e) = world_client_manager
                .add_client_connection(
                    session_id.clone(),
                    client_id,
                    map_id,
                    world_url,
                )
                .await
            {
                error!("Failed to connect client to world service: {}", e);
                return Err(e);
            }

            info!("Connected client {} to world service on map {}", session_id, map_id);

            // Start world event handler for this client
            start_world_event_handler(world_client_manager.clone(), connection_service.clone(), session_id.clone());
        } else {
            warn!("Character spawn requested but session_id or character_id not set");
        }
    }

    Ok(())
}

pub async fn handle_character_move_world(
    packet: Packet,
    world_client_manager: Arc<WorldClientManager>,
    connection_service: Arc<ConnectionService>,
    connection_id: String,
) -> Result<(), Box<dyn Error + Send + Sync>> {
    debug!("Handling character move packet for world service: {:?}", packet);

    if let Some(connection_state) = connection_service.get_connection(&connection_id) {
        if let Some(session_id) = &connection_state.session_id {
            // Extract movement data from packet
            // This is a simplified example - you'd need to parse the actual packet data
            let x = 100.0; // Extract from packet.data
            let y = 100.0; // Extract from packet.data
            let z = 0.0;   // Extract from packet.data

            // Send movement event to world service
            if let Err(e) = world_client_manager
                .send_client_move_event(session_id, x, y, z)
                .await
            {
                error!("Failed to send move event to world service: {}", e);
                return Err(e);
            }

            debug!("Sent move event for client {} to world service", session_id);
        } else {
            warn!("Character move requested but session_id not set");
        }
    }

    Ok(())
}

pub async fn handle_get_nearby_objects(
    packet: Packet,
    world_client_manager: Arc<WorldClientManager>,
    connection_service: Arc<ConnectionService>,
    connection_id: String,
) -> Result<(), Box<dyn Error + Send + Sync>> {
    debug!("Handling get nearby objects packet: {:?}", packet);

    if let Some(connection_state) = connection_service.get_connection(&connection_id) {
        if let Some(session_id) = &connection_state.session_id {
            // Extract position data from packet
            let x = 100.0; // Extract from packet.data
            let y = 100.0; // Extract from packet.data
            let z = 0.0;   // Extract from packet.data
            let map_id = 1; // Extract from connection state or packet
            let radius = 50.0; // Default radius

            // Get nearby objects from world service
            match world_client_manager
                .get_nearby_objects(session_id, x, y, z, map_id, radius)
                .await
            {
                Ok(response) => {
                    debug!("Received {} nearby objects for client {}", response.objects.len(), session_id);

                    // Convert world objects to game packets and send to client
                    // This would involve creating appropriate response packets
                    // and sending them through the connection's writer

                    // For now, just log the objects
                    for object in response.objects {
                        debug!("Nearby object: id={}, type={}, pos=({}, {}, {})",
                               object.id, object.object_type, object.x, object.y, object.z);
                    }
                }
                Err(e) => {
                    error!("Failed to get nearby objects from world service: {}", e);
                    return Err(e);
                }
            }
        } else {
            warn!("Get nearby objects requested but session_id not set");
        }
    }

    Ok(())
}

async fn handle_world_events(
    world_client_manager: Arc<WorldClientManager>,
    connection_service: Arc<ConnectionService>,
    session_id: String,
) {
    info!("Starting world event handler for session {}", session_id);

    loop {
        match world_client_manager.receive_world_event(&session_id).await {
            Some(world_event) => {
                debug!("Received world event for session {}: {:?}", session_id, world_event);

                // Process the world event and convert to game packets
                match world_event.event {
                    Some(crate::world_client::world::world_event::Event::NpcSpawn(npc_spawn)) => {
                        debug!("Processing NPC spawn event: {:?}", npc_spawn);
                        // Convert to appropriate game packet and send to client
                        // This would involve creating a spawn packet and sending it through the connection's writer
                    }
                    Some(crate::world_client::world::world_event::Event::MobSpawn(mob_spawn)) => {
                        debug!("Processing mob spawn event: {:?}", mob_spawn);
                        // Convert to appropriate game packet and send to client
                    }
                    Some(crate::world_client::world::world_event::Event::ObjectDespawn(despawn)) => {
                        debug!("Processing object despawn event: {:?}", despawn);
                        // Convert to appropriate game packet and send to client
                    }
                    Some(crate::world_client::world::world_event::Event::NearbyUpdate(nearby_update)) => {
                        debug!("Processing nearby objects update: {} objects", nearby_update.objects.len());
                        // Convert to appropriate game packet and send to client
                    }
                    None => {
                        warn!("Received world event with no event data for session {}", session_id);
                    }
                }
            }
            None => {
                debug!("World event stream ended for session {}", session_id);
                break;
            }
        }
    }

    info!("World event handler ended for session {}", session_id);
}

// Helper function to start world event handling for a client
pub fn start_world_event_handler(
    world_client_manager: Arc<WorldClientManager>,
    connection_service: Arc<ConnectionService>,
    session_id: String,
) {
    tokio::spawn(async move {
        handle_world_events(world_client_manager, connection_service, session_id).await;
    });
}